//
//  VideoDownloadOptionsSheet.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

struct VideoDownloadOptionsSheet: View {
    @ObservedObject var downloadViewModel: VideoDownloadViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if let video = downloadViewModel.selectedVideo {
                    // Video Info Section
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            CachedAsyncImage(url: video.imageUrl) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.3))
                            }
                            .frame(width: 120, height: 68)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text(video.title)
                                    .font(.headline)
                                    .lineLimit(2)
                                
                                if let channel = video.channel {
                                    Text(channel.name)
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                
                                Text(video.formattedDuration)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    
                    // Quality Selection Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Select Quality")
                            .font(.headline)
                        
                        if downloadViewModel.isLoadingQualities {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("Loading available qualities...")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                            .padding()
                        } else {
                            LazyVStack(spacing: 8) {
                                ForEach(downloadViewModel.availableQualities) { quality in
                                    QualitySelectionRow(
                                        quality: quality,
                                        isSelected: downloadViewModel.selectedQuality?.id == quality.id
                                    ) {
                                        downloadViewModel.selectedQuality = quality
                                    }
                                }
                            }
                        }
                    }
                    
                    Spacer()
                    
                    // Download Button
                    Button(action: {
                        Task {
                            dismiss()
                            await downloadViewModel.downloadVideo()
                        }
                    }) {
                        HStack {
                            Image(systemName: "arrow.down.circle.fill")
                            Text("Download Video")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            downloadViewModel.selectedQuality != nil && !downloadViewModel.isLoadingQualities
                                ? Color.blue
                                : Color.gray
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                    .disabled(downloadViewModel.selectedQuality == nil || downloadViewModel.isLoadingQualities)
                }
            }
            .padding()
            .navigationTitle("Download Video")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        downloadViewModel.dismissDownloadOptions()
                        dismiss()
                    }
                }
            }
        }
        .alert("Download Error", isPresented: $downloadViewModel.showingDownloadError) {
            Button("OK") {
                downloadViewModel.dismissError()
            }
        } message: {
            if let error = downloadViewModel.downloadError {
                Text(error.localizedDescription)
            }
        }
    }
}

struct QualitySelectionRow: View {
    let quality: VideoQuality
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(quality.displayName)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    if quality.bandwidth > 0 {
                        Text("Bandwidth: \(formatBandwidth(quality.bandwidth))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatBandwidth(_ bandwidth: Int) -> String {
        let mbps = Double(bandwidth) / 1_000_000
        return String(format: "%.1f Mbps", mbps)
    }
}

#Preview {
    VideoDownloadOptionsSheet(downloadViewModel: VideoDownloadViewModel())
}

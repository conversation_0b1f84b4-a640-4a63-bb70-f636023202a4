//
//  LiveStreamVScrollView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import SwiftUI

struct LiveStreamVScrollView: View {
    let streams: [UILiveStream]
    @Binding var selectedStream: UILiveStream?
    let onLoadMore: () async -> Void
    let isLoading: Bool
    var onStreamHidden: ((UILiveStream) -> Void)?
    
    @Environment(\.channelService) private var channelService
    
    // State dictionaries to track follow/hide status for each channel
    @State private var followStatus: [String: Bool] = [:]
    
    private func columns(for width: CGFloat) -> [GridItem] {
#if os(tvOS)
        return [GridItem(.adaptive(minimum: Constants.videoCardWidth), alignment: .top)]
#else
        // Use actual screen width to determine layout
        // Portrait: width < 600, Landscape: width >= 600
        if width < 600 {
            return [GridItem(.flexible(), alignment: .top)]
        } else {
            return [GridItem(.flexible(), alignment: .top), GridItem(.flexible(), alignment: .top)]
        }
#endif
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(columns: columns(for: geometry.size.width), spacing: Constants.cardSpacing) {
                    if isLoading && streams.isEmpty {
                        ForEach(0..<12) { _ in Button {

                        } label: {
                            LiveStreamCard(stream: nil)
                                .aspectRatio(16/9, contentMode: .fit)
                        }
                        #if os(tvOS)
                        .buttonStyle(.card)
                        #else
                        .buttonStyle(.plain)
                        #endif
                        }
                    } else {
                        ForEach(streams) { stream in Button {
                            if !stream.isAdult, !stream.isAbroad {
                                selectedStream = stream
                            }
                        } label: {
                            LiveStreamCard(stream: stream)
                                .aspectRatio(16/9, contentMode: .fit)
                        }
                        #if os(tvOS)
                        .buttonStyle(.card)
                        #else
                        .buttonStyle(.plain)
                        #endif
                            .contextMenu {
                                contextMenu(stream: stream)
                            }
                        }
                    }
                    
                    if !streams.isEmpty {
                        Color.clear
                            .frame(height: 50)
                            .onAppear {
                                Task {
                                    await onLoadMore()
                                }
                            }
                    }
                }
                .padding(Constants.cardPadding)
            }
        }
        .onAppear {
            // Initialize status dictionaries when view appears
            for stream in streams {
                if let channel = stream.channel {
                    followStatus[channel.id] = channelService.isFollowing(channelId: channel.id)
                }
            }
        }
    }
    
    @ViewBuilder
    func contextMenu(stream: UILiveStream) -> some View {
        if let channel = stream.channel {
            CardContextMenu(
                channel: channel,
                followStatus: Binding(
                    get: { followStatus[channel.id] ?? false },
                    set: { followStatus[channel.id] = $0 }
                )
            )
            .onAppear {
                if followStatus[channel.id] == nil {
                    followStatus[channel.id] = channelService.isFollowing(channelId: channel.id)
                }
            }
        }
    }
}

#Preview("Default") {
    LiveStreamVScrollView(
        streams: StreamPreview.createSampleUILiveStreams(count: 20),
        selectedStream: .constant(nil),
        onLoadMore: { },
        isLoading: false
    )
    .padding()
}

#Preview("Multiple Columns") {
    LiveStreamVScrollView(
        streams: StreamPreview.createSampleUILiveStreams(count: 20),
        selectedStream: .constant(nil),
        onLoadMore: { },
        isLoading: false
    )
    .frame(width: 800) // Force landscape width to test multiple columns
}

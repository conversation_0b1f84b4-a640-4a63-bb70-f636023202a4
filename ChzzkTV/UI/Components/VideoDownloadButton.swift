//
//  VideoDownloadButton.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

struct VideoDownloadButton: View {
    let video: UIVideo
    @ObservedObject var downloadViewModel: VideoDownloadViewModel
    @State private var showingDownloadOptions = false
    
    var body: some View {
        Button(action: {
            handleDownloadAction()
        }) {
            downloadButtonContent
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDownloadOptions) {
            VideoDownloadOptionsSheet(downloadViewModel: downloadViewModel)
        }
    }
    
    @ViewBuilder
    private var downloadButtonContent: some View {
        let downloadState = downloadViewModel.getDownloadState(for: video)
        
        switch downloadState {
        case .none:
            // Not downloaded - show download button
            Image(systemName: "arrow.down.circle")
                .font(.title2)
                .foregroundColor(.blue)
            
        case .pending:
            // Pending download
            Image(systemName: "clock.circle")
                .font(.title2)
                .foregroundColor(.orange)
            
        case .downloading(let progress):
            // Currently downloading - show progress
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                    .frame(width: 24, height: 24)
                
                Circle()
                    .trim(from: 0, to: progress)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 2, lineCap: .round))
                    .frame(width: 24, height: 24)
                    .rotationEffect(.degrees(-90))
                
                Text("\(Int(progress * 100))")
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(.blue)
            }
            
        case .processing:
            // Processing after download
            ProgressView()
                .scaleEffect(0.8)
                .tint(.blue)
            
        case .completed:
            // Downloaded - show checkmark with ShareLink
            if let fileURL = downloadedFileURL {
                ShareLink(item: fileURL) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                }
            } else {
                // Fallback if file URL is not available
                Image(systemName: "checkmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.green)
                    .onTapGesture {
                        VideoDownloadService.shared.openFilesApp()
                    }
            }
            
        case .failed:
            // Failed - show error icon
            Image(systemName: "exclamationmark.circle.fill")
                .font(.title2)
                .foregroundColor(.red)
            
        case .cancelled:
            // Cancelled - show download button again
            Image(systemName: "arrow.down.circle")
                .font(.title2)
                .foregroundColor(.blue)
        }
    }
    
    private func handleDownloadAction() {
        let downloadState = downloadViewModel.getDownloadState(for: video)
        
        switch downloadState {
        case .none, .cancelled, .failed:
            // Start new download
            downloadViewModel.startDownload(for: video)
            showingDownloadOptions = true
            
        case .pending, .downloading, .processing:
            // Cancel active download
            downloadViewModel.cancelDownload(for: video)
            
        case .completed:
            // Already downloaded - ShareLink will handle the sharing
            // No action needed here as ShareLink handles the tap
            break
        }
    }
    
    private var downloadedFileURL: URL? {
        // Get the completed download info for this video
        if let downloadInfo = VideoDownloadService.shared.completedDownloads[video.id] {
            return VideoDownloadService.shared.getDownloadedFileURL(for: downloadInfo)
        }
        return nil
    }
}

// MARK: - Compact Version for Smaller UI Elements

struct CompactVideoDownloadButton: View {
    let video: UIVideo
    @ObservedObject var downloadViewModel: VideoDownloadViewModel
    @State private var showingDownloadOptions = false

    private var downloadedFileURL: URL? {
        // Get the completed download info for this video
        if let downloadInfo = VideoDownloadService.shared.completedDownloads[video.id] {
            return VideoDownloadService.shared.getDownloadedFileURL(for: downloadInfo)
        }
        return nil
    }
    
    var body: some View {
        Button(action: {
            handleDownloadAction()
        }) {
            downloadButtonContent
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDownloadOptions) {
            VideoDownloadOptionsSheet(downloadViewModel: downloadViewModel)
        }
    }
    
    @ViewBuilder
    private var downloadButtonContent: some View {
        let downloadState = downloadViewModel.getDownloadState(for: video)
        
        switch downloadState {
        case .none, .cancelled, .failed:
            Image(systemName: "arrow.down.circle")
                .font(.caption)
                .foregroundColor(.blue)
            
        case .pending:
            Image(systemName: "clock.circle")
                .font(.caption)
                .foregroundColor(.orange)
            
        case .downloading(let progress):
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    .frame(width: 12, height: 12)
                
                Circle()
                    .trim(from: 0, to: progress)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 1, lineCap: .round))
                    .frame(width: 12, height: 12)
                    .rotationEffect(.degrees(-90))
            }
            
        case .processing:
            ProgressView()
                .scaleEffect(0.5)
                .tint(.blue)
            
        case .completed:
            if let fileURL = downloadedFileURL {
                ShareLink(item: fileURL) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            } else {
                Image(systemName: "checkmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.green)
                    .onTapGesture {
                        VideoDownloadService.shared.openFilesApp()
                    }
            }
        }
    }
    
    private func handleDownloadAction() {
        let downloadState = downloadViewModel.getDownloadState(for: video)
        
        switch downloadState {
        case .none, .cancelled, .failed:
            downloadViewModel.startDownload(for: video)
            showingDownloadOptions = true
            
        case .pending, .downloading, .processing:
            downloadViewModel.cancelDownload(for: video)
            
        case .completed:
            // ShareLink will handle the sharing
            // No action needed here as ShareLink handles the tap
            break
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        VideoDownloadButton(
            video: StreamPreview.sampleUIVideo,
            downloadViewModel: VideoDownloadViewModel()
        )
        
        CompactVideoDownloadButton(
            video: StreamPreview.sampleUIVideo,
            downloadViewModel: VideoDownloadViewModel()
        )
    }
    .padding()
}

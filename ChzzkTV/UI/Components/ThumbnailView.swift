//
//  ThumbnailView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 4/4/25.
//

import SwiftUI

struct ThumbnailView: View {
    @State private var dataUrl: URL?
    let thumbnail: Thumbnail?

    var body: some View {
        if thumbnail?.imageUrl != nil {
            AsyncImage(url: dataUrl) { image in
                image
                    .resizable()
                    .aspectRatio(16/9, contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .aspectRatio(16/9, contentMode: .fill)
            }
            .clipped()
            .onDisappear {
                dataUrl = nil
            }
            .onAppear{
                dataUrl = thumbnail?.imageUrl
            }
        } else {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .aspectRatio(16/9, contentMode: .fill)
                .overlay {
                    VStack(spacing: 12) {
                        if thumbnail?.isAdult ?? false {
                            Image(systemName: "19.circle")
                                .font(.system(size: 30))
                            Text("Adult Content")
                                .font(.subheadline)
                        } else if thumbnail?.isAbroad ?? false {
                            Image(systemName: "exclamationmark.circle")
                                .font(.system(size: 30))
                            Text("Foreign Content")
                                .font(.subheadline)
                        }
                    }
                }
        }
    }
}

fileprivate struct MockThumbnail: Thumbnail {
    init(imageUrl: URL?, isAbroad: Bool, isAdult: Bool) {
        self.imageUrl = imageUrl
        self.isAbroad = isAbroad
        self.isAdult = isAdult
    }
    var imageUrl: URL?
    var isAbroad: Bool
    var isAdult: Bool
}

#Preview("Abroad") {
    ThumbnailView(thumbnail: MockThumbnail(imageUrl: nil, isAbroad: true, isAdult: false))
}

#Preview("Adult") {
    ThumbnailView(thumbnail: MockThumbnail(imageUrl: nil, isAbroad: false, isAdult: true))
}

#Preview("Nil") {
    ThumbnailView(thumbnail: nil)
}

#Preview("Load Image") {
    ThumbnailView(thumbnail: MockThumbnail(imageUrl: URL(string: "https://picsum.photos/200")!, isAbroad: true, isAdult: false))
}

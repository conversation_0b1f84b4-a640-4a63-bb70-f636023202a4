import SwiftUI
import AVKit
import SwiftData

struct VideoDetailView: View {
    let video: UIVideo
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel: VideoViewModel
    @StateObject private var downloadViewModel = VideoDownloadViewModel()

    init(video: UIVideo, viewModel: VideoViewModel) {
        self.video = video
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        ZStack {
            // Video Player with channel info
            if viewModel.player != nil {
                VideoPlayerView(
                    player: viewModel.player,
                    channel: video.channel,
                    availableQualities: viewModel.availableQualities,
                    currentQuality: viewModel.currentQuality,
                    onQualityChange: { quality in
                        Task {
                            await viewModel.changeQuality(quality)
                        }
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .ignoresSafeArea()
                .forceLandscape()
            } else {
                // Display a thumbnail as a placeholder while loading
                ZStack {
                    thumbnailView

                    // Download button overlay
                    VStack {
                        HStack {
                            Spacer()
                            VideoDownloadButton(video: video, downloadViewModel: downloadViewModel)
                                .padding()
                        }
                        Spacer()
                    }
                }
            }
            
            // Loading indicator
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(2.0)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
            
            // Error overlay
            if viewModel.error != nil {
                ErrorOverlayView(
                    message: "Unable to load video\n\(viewModel.error?.localizedDescription ?? "")",
                    onRetry: {
                        Task {
                            viewModel.resetError()
                            await viewModel.loadVideo(video.id)
                        }
                    },
                    onDismiss: {
                        dismiss()
                    }
                )
            }
        }
        #if os(tvOS)
        .onPlayPauseCommand {
            viewModel.togglePlayback()
        }
        #endif
        .task {
            print("VideoDetailView task started")
            await viewModel.loadVideo(video.id)
        }
        .onDisappear {
            print("VideoDetailView disappeared - cleaning up")
            viewModel.prepareForDismissal()
        }
    }
    
    private var thumbnailView: some View {
        ZStack {
            Color.black
            
            if let url = video.imageUrl {
                CachedAsyncImage(url: url) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    Rectangle()
                        .foregroundColor(.gray.opacity(0.3))
                }
                .aspectRatio(16/9, contentMode: .fit)
            }
        }
        .edgesIgnoringSafeArea(.all)
    }
}

#Preview {
    PreviewData.createPreviewVideoDetailView()
}

//
//  DownloadsView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

struct DownloadsView: View {
    @ObservedObject private var downloadService = VideoDownloadService.shared
    @State private var selectedDownload: VideoDownloadInfo?
    @State private var showingDeleteAlert = false
    
    var body: some View {
        VStack {
                if downloadService.activeDownloads.isEmpty &&
                   downloadService.completedDownloads.isEmpty {
                    // Empty state
                    VStack(spacing: 16) {
                        Image(systemName: "arrow.down.circle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No Downloads")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("Downloaded videos will appear here")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List {
                        // Active Downloads Section
                        if !downloadService.activeDownloads.isEmpty {
                            Section("Active Downloads") {
                                ForEach(Array(downloadService.activeDownloads.values), id: \.video.id) { download in
                                    ActiveDownloadRow(download: download, onCancel: {
                                        downloadService.cancelDownload(for: download.video.id)
                                    })
                                }
                            }
                        }
                        
                        // Completed Downloads Section
                        if !downloadService.completedDownloads.isEmpty {
                            Section("Downloaded Videos") {
                                ForEach(Array(downloadService.completedDownloads.values), id: \.video.id) { download in
                                    CompletedDownloadRow(
                                        download: download,
                                        fileURL: downloadService.getDownloadedFileURL(for: download),
                                        onDelete: {
                                            selectedDownload = download
                                            showingDeleteAlert = true
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
        }
        .navigationTitle("Downloads")
        .navigationBarTitleDisplayMode(.inline)
        .refreshable {
            // Refresh the downloads list
            downloadService.objectWillChange.send()
        }
        .alert("Delete Download", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                if let download = selectedDownload {
                    try? downloadService.deleteDownloadedVideo(for: download.video.id)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            if let download = selectedDownload {
                Text("Are you sure you want to delete '\(download.video.title)'?")
            }
        }
    }
}

struct ActiveDownloadRow: View {
    let download: VideoDownloadInfo
    let onCancel: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Thumbnail
            CachedAsyncImage(url: download.video.imageUrl) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }
            .frame(width: 80, height: 45)
            .clipShape(RoundedRectangle(cornerRadius: 6))
            
            // Video Info
            VStack(alignment: .leading, spacing: 4) {
                Text(download.video.title)
                    .font(.headline)
                    .lineLimit(2)
                
                if let channel = download.video.channel {
                    Text(channel.name)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // Download Progress
                VStack(alignment: .leading, spacing: 2) {
                    Text(download.state.displayText)
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    if case .downloading(let progress) = download.state {
                        ProgressView(value: progress)
                            .progressViewStyle(.circular)
                            .scaleEffect(0.8)
                    } else if case .processing = download.state {
                        ProgressView()
                            .progressViewStyle(.circular)
                            .scaleEffect(0.8)
                    }
                }
            }
            
            Spacer()

            // Progress and Cancel Button
            HStack(spacing: 8) {
                // Circular progress indicator
                if case .downloading(let progress) = download.state {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.3), lineWidth: 3)
                            .frame(width: 32, height: 32)

                        Circle()
                            .trim(from: 0, to: progress)
                            .stroke(Color.blue, style: StrokeStyle(lineWidth: 3, lineCap: .round))
                            .frame(width: 32, height: 32)
                            .rotationEffect(.degrees(-90))

                        Text("\(Int(progress * 100))%")
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                } else if case .processing = download.state {
                    ProgressView()
                        .progressViewStyle(.circular)
                        .scaleEffect(0.8)
                        .frame(width: 32, height: 32)
                }

                // Cancel Button
                Button(action: onCancel) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.red)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
    }
}

struct CompletedDownloadRow: View {
    let download: VideoDownloadInfo
    let fileURL: URL?
    let onDelete: () -> Void

    private var downloadRowContent: some View {
        HStack(spacing: 12) {
            // Thumbnail
            CachedAsyncImage(url: download.video.imageUrl) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }
            .frame(width: 80, height: 45)
            .clipShape(RoundedRectangle(cornerRadius: 6))

            // Video Info
            VStack(alignment: .leading, spacing: 4) {
                Text(download.video.title)
                    .font(.headline)
                    .lineLimit(2)

                if let channel = download.video.channel {
                    Text(channel.name)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                HStack {
                    Text(download.quality.displayName)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .clipShape(RoundedRectangle(cornerRadius: 4))

                    Text(download.formattedFileSize)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }

            Spacer()

            // Downloaded indicator
            Image(systemName: "checkmark.circle.fill")
                .font(.title2)
                .foregroundColor(.green)
        }
    }
    
    var body: some View {
        Group {
            if let fileURL = fileURL {
                ShareLink(item: fileURL) {
                    downloadRowContent
                }
            } else {
                // Fallback if file URL is not available
                Button(action: {
                    VideoDownloadService.shared.openFilesApp()
                }) {
                    downloadRowContent
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
        }
        .contextMenu {
            Button(action: onDelete) {
                Label("Delete", systemImage: "trash")
            }
        }
    }
}

#Preview {
    DownloadsView()
}

//
//  ContentViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/28/25.
//

import SwiftUI
import Combine

enum Tabs: String {
    case login, home, live, search, category, downloads
}

@MainActor
final class ContentViewModel: ObservableObject {
    @AppStorage("ContentView.selectedTab") private(set) var selectedTab: Tabs = .live
    @Published private(set) var channelToOpen: String? = nil
    
    private var cancellables: Set<AnyCancellable> = []
    
    init() {
        setupNotificationObservers()
    }
    
    private func setupNotificationObservers() {
        // Channel navigation observer
        NotificationCenter.default.publisher(for: .navigateToChannel)
            .compactMap { $0.object as? String }
            .assign(to: \.channelToOpen, on: self)
            .store(in: &cancellables)
        
        // Tab navigation observer
        NotificationCenter.default.publisher(for: .navigateToTab)
            .compactMap { $0.object as? String }
            .compactMap { Tabs(rawValue: $0) }
            .assign(to: \.selectedTab, on: self)
            .store(in: &cancellables)
    }
    
    func setSelectedTab(_ tab: Tabs) {
        selectedTab = tab
    }
    
    func clearChannelNavigation() {
        channelToOpen = nil
    }
    
    // Redirects from Home/Downloads tab if user is not logged in or on tvOS
    func handleTabSelection(_ tab: Tabs, isLoggedIn: Bool) -> Tabs {
        #if os(tvOS)
        // On tvOS, downloads tab is not available, redirect to live
        if tab == .downloads {
            return .live
        }
        #endif

        if (tab == .home || tab == .downloads) && !isLoggedIn {
            return .live
        }
        return tab
    }
}
